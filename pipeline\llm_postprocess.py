"""
LLM Post-Processing Module for AutoSub with LLM

This module provides LLM-enhanced transcription post-processing using either
Google Gemini or local Aya Expanse models. The LLMs process batches of raw 
ASR transcriptions to improve accuracy and maintain contextual coherence.

Features:
- Dual LLM support (Google Gemini / Local Aya Expanse)
- Batch processing with contextual understanding
- Configurable prompts and parameters
- Error correction and punctuation improvement
- Speaker consistency maintenance
"""

import os
import sys
import json
import logging
from pathlib import Path
from typing import List, Dict, Any, Optional, Union
import tempfile
from tqdm import tqdm
import time

# Setup logging
logger = logging.getLogger(__name__)

# Import LLM libraries with fallbacks
try:
    import google.generativeai as genai
    GEMINI_AVAILABLE = True
except ImportError:
    GEMINI_AVAILABLE = False
    logger.warning("⚠️  Google Generative AI not available. Install with: pip install google-generativeai")

try:
    from llama_cpp import Llama
    LLAMA_CPP_AVAILABLE = True
except ImportError:
    LLAMA_CPP_AVAILABLE = False
    logger.warning("⚠️  llama-cpp-python not available. Install with: pip install llama-cpp-python")


class LLMPostProcessor:
    """Base class for LLM post-processing."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize LLM post-processor with configuration."""
        self.config = config
        self.batch_size = config.get("llm_batch_size", 8)
        self.provider = config.get("llm_provider", "gemini")
        
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using LLM. To be implemented by subclasses."""
        raise NotImplementedError
    
    def _create_enhancement_prompt(self, transcription_batch: List[str]) -> str:
        """Create prompt for LLM enhancement."""
        prompt_template = self.config.get("llm_prompt_template", """
You are an expert multilingual transcription editor with deep knowledge of Vietnamese, English, and other languages. Your task is to fix and improve the following batch of ASR (Automatic Speech Recognition) transcriptions for accuracy, context, and linguistic correctness.

CRITICAL INSTRUCTIONS:
1. **Language Detection & Preservation**: Automatically detect the language(s) being spoken and maintain linguistic authenticity
2. **ASR Error Correction**: Fix common ASR misinterpretations, especially for:
   - Vietnamese tones (á, à, ả, ã, ạ, ă, ắ, ằ, ẳ, ẵ, ặ, â, ấ, ầ, ẩ, ẫ, ậ, etc.)
   - Vietnamese consonants (đ, ng, nh, ph, th, tr, ch, kh, gh, etc.)
   - English homophones (there/their/they're, to/too/two, etc.)
   - Technical terms and proper nouns
3. **Punctuation & Formatting**: Add proper punctuation, capitalization, and sentence structure
4. **Contextual Coherence**: Ensure segments flow logically and maintain speaker consistency
5. **Cultural Context**: Preserve cultural references, idioms, and language-specific expressions
6. **Timing Preservation**: Do not alter the semantic timing or add content that wasn't spoken
7. **Output Format**: Return exactly the same number of segments in the same order

LANGUAGE-SPECIFIC EXAMPLES:

Vietnamese Examples:
- ASR: "toi den truong hoc" → Enhanced: "Tôi đến trường học"
- ASR: "cam on ban rat nhieu" → Enhanced: "Cảm ơn bạn rất nhiều"
- ASR: "chung ta se lam viec cung nhau" → Enhanced: "Chúng ta sẽ làm việc cùng nhau"
- ASR: "hom nay troi dep qua" → Enhanced: "Hôm nay trời đẹp quá"

English Examples:
- ASR: "there going to the store" → Enhanced: "They're going to the store"
- ASR: "i cant here you" → Enhanced: "I can't hear you"
- ASR: "its a beautiful day" → Enhanced: "It's a beautiful day"

Mixed Language Examples:
- ASR: "toi dang lam viec voi artificial intelligence" → Enhanced: "Tôi đang làm việc với artificial intelligence"
- ASR: "meeting se bat dau luc 2 pm" → Enhanced: "Meeting sẽ bắt đầu lúc 2 PM"

Technical Terms:
- ASR: "machine learning algorithm" → Enhanced: "machine learning algorithm" (preserve technical accuracy)
- ASR: "covid nineteen pandemic" → Enhanced: "COVID-19 pandemic"

Input transcriptions:
{transcriptions}

Enhanced transcriptions (one per line, same order):
""")
        
        # Format transcriptions for the prompt
        formatted_transcriptions = "\n".join([
            f"{i+1}. {trans}" for i, trans in enumerate(transcription_batch)
        ])
        
        return prompt_template.format(transcriptions=formatted_transcriptions)


class GeminiPostProcessor(LLMPostProcessor):
    """Google Gemini LLM post-processor."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Gemini post-processor."""
        super().__init__(config)
        self.model = None
        self._setup_gemini()
    
    def _setup_gemini(self) -> bool:
        """Setup Gemini API."""
        if not GEMINI_AVAILABLE:
            logger.error("❌ Google Generative AI not available")
            return False
        
        try:
            # Get API key from config or environment
            api_key = self.config.get("gemini_api_key") or os.getenv("GEMINI_API_KEY")
            if not api_key:
                logger.error("❌ Gemini API key not found. Set GEMINI_API_KEY environment variable.")
                return False
            
            # Configure Gemini
            genai.configure(api_key=api_key)
            
            # Initialize model with custom configuration
            model_name = self.config.get("gemini_model", "gemini-1.5-flash")

            # Support for custom model configurations
            generation_config = {}
            if self.config.get("gemini_temperature"):
                generation_config["temperature"] = self.config.get("gemini_temperature")
            if self.config.get("gemini_max_output_tokens"):
                generation_config["max_output_tokens"] = self.config.get("gemini_max_output_tokens")
            if self.config.get("gemini_top_p"):
                generation_config["top_p"] = self.config.get("gemini_top_p")
            if self.config.get("gemini_top_k"):
                generation_config["top_k"] = self.config.get("gemini_top_k")

            # Initialize model with custom config if provided
            if generation_config:
                self.model = genai.GenerativeModel(
                    model_name=model_name,
                    generation_config=generation_config
                )
                logger.info(f"✅ Gemini model initialized: {model_name} with custom config: {generation_config}")
            else:
                self.model = genai.GenerativeModel(model_name)
                logger.info(f"✅ Gemini model initialized: {model_name}")

            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup Gemini: {str(e)}")
            return False
    
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using Gemini."""
        if not self.model:
            logger.error("❌ Gemini model not initialized")
            return transcriptions
        
        enhanced_results = []
        successful_transcriptions = [t for t in transcriptions if t.get("success", False)]
        
        if not successful_transcriptions:
            logger.warning("⚠️  No successful transcriptions to enhance")
            return transcriptions
        
        logger.info(f"🤖 Enhancing {len(successful_transcriptions)} transcriptions with Gemini...")
        
        try:
            # Process in batches
            for i in tqdm(range(0, len(successful_transcriptions), self.batch_size), desc="LLM Enhancement"):
                batch = successful_transcriptions[i:i + self.batch_size]
                batch_texts = [t["transcription"] for t in batch]
                
                try:
                    # Create prompt
                    prompt = self._create_enhancement_prompt(batch_texts)
                    
                    # Generate enhanced transcriptions
                    response = self.model.generate_content(prompt)
                    enhanced_texts = self._parse_gemini_response(response.text, len(batch_texts))
                    
                    # Update results
                    for j, (original, enhanced_text) in enumerate(zip(batch, enhanced_texts)):
                        enhanced_result = original.copy()
                        enhanced_result["original_transcription"] = original["transcription"]
                        enhanced_result["transcription"] = enhanced_text
                        enhanced_result["enhanced_by"] = "gemini"
                        enhanced_results.append(enhanced_result)
                    
                    # Rate limiting
                    time.sleep(self.config.get("gemini_rate_limit", 1.0))
                    
                except Exception as e:
                    logger.error(f"❌ Gemini batch enhancement failed: {str(e)}")
                    # Keep original transcriptions for failed batch
                    for original in batch:
                        enhanced_result = original.copy()
                        enhanced_result["enhanced_by"] = "failed"
                        enhanced_result["enhancement_error"] = str(e)
                        enhanced_results.append(enhanced_result)
            
            # Add back failed transcriptions
            for t in transcriptions:
                if not t.get("success", False):
                    enhanced_results.append(t)
            
            successful_enhanced = sum(1 for r in enhanced_results if r.get("enhanced_by") == "gemini")
            logger.info(f"✅ Gemini enhancement completed: {successful_enhanced}/{len(successful_transcriptions)} enhanced")
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"❌ Gemini enhancement error: {str(e)}")
            return transcriptions
    
    def _parse_gemini_response(self, response_text: str, expected_count: int) -> List[str]:
        """Parse Gemini response to extract enhanced transcriptions."""
        try:
            lines = response_text.strip().split('\n')
            enhanced_texts = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('Enhanced'):
                    # Remove numbering if present
                    if '. ' in line and line.split('. ', 1)[0].isdigit():
                        line = line.split('. ', 1)[1]
                    enhanced_texts.append(line)
            
            # Ensure we have the expected number of results
            while len(enhanced_texts) < expected_count:
                enhanced_texts.append("")  # Add empty strings for missing results
            
            return enhanced_texts[:expected_count]
            
        except Exception as e:
            logger.error(f"❌ Failed to parse Gemini response: {str(e)}")
            return [""] * expected_count


class AyaExpansePostProcessor(LLMPostProcessor):
    """Local Aya Expanse LLM post-processor."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize Aya Expanse post-processor."""
        super().__init__(config)
        self.model = None
        self._setup_aya_expanse()
    
    def _setup_aya_expanse(self) -> bool:
        """Setup Aya Expanse model."""
        if not LLAMA_CPP_AVAILABLE:
            logger.error("❌ llama-cpp-python not available")
            return False
        
        try:
            logger.info("🔄 Loading Aya Expanse model...")
            
            # Model configuration
            model_config = {
                "repo_id": "lmstudio-community/aya-expanse-8b-GGUF",
                "filename": "aya-expanse-8b-Q4_K_M.gguf",
                "n_ctx": self.config.get("aya_context_length", 4096),
                "n_threads": self.config.get("aya_threads", 4),
                "verbose": False
            }
            
            # Load model
            self.model = Llama.from_pretrained(**model_config)
            
            logger.info("✅ Aya Expanse model loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to setup Aya Expanse: {str(e)}")
            return False
    
    def enhance_transcriptions(self, transcriptions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance transcriptions using Aya Expanse."""
        if not self.model:
            logger.error("❌ Aya Expanse model not initialized")
            return transcriptions
        
        enhanced_results = []
        successful_transcriptions = [t for t in transcriptions if t.get("success", False)]
        
        if not successful_transcriptions:
            logger.warning("⚠️  No successful transcriptions to enhance")
            return transcriptions
        
        logger.info(f"🤖 Enhancing {len(successful_transcriptions)} transcriptions with Aya Expanse...")
        
        try:
            # Process in batches
            for i in tqdm(range(0, len(successful_transcriptions), self.batch_size), desc="LLM Enhancement"):
                batch = successful_transcriptions[i:i + self.batch_size]
                batch_texts = [t["transcription"] for t in batch]
                
                try:
                    # Create prompt
                    prompt = self._create_enhancement_prompt(batch_texts)
                    
                    # Generate enhanced transcriptions
                    response = self.model.create_chat_completion(
                        messages=[
                            {
                                "role": "user",
                                "content": prompt
                            }
                        ],
                        max_tokens=self.config.get("aya_max_tokens", 1024),
                        temperature=self.config.get("aya_temperature", 0.3)
                    )
                    
                    response_text = response['choices'][0]['message']['content']
                    enhanced_texts = self._parse_aya_response(response_text, len(batch_texts))
                    
                    # Update results
                    for j, (original, enhanced_text) in enumerate(zip(batch, enhanced_texts)):
                        enhanced_result = original.copy()
                        enhanced_result["original_transcription"] = original["transcription"]
                        enhanced_result["transcription"] = enhanced_text
                        enhanced_result["enhanced_by"] = "aya-expanse"
                        enhanced_results.append(enhanced_result)
                    
                except Exception as e:
                    logger.error(f"❌ Aya Expanse batch enhancement failed: {str(e)}")
                    # Keep original transcriptions for failed batch
                    for original in batch:
                        enhanced_result = original.copy()
                        enhanced_result["enhanced_by"] = "failed"
                        enhanced_result["enhancement_error"] = str(e)
                        enhanced_results.append(enhanced_result)
            
            # Add back failed transcriptions
            for t in transcriptions:
                if not t.get("success", False):
                    enhanced_results.append(t)
            
            successful_enhanced = sum(1 for r in enhanced_results if r.get("enhanced_by") == "aya-expanse")
            logger.info(f"✅ Aya Expanse enhancement completed: {successful_enhanced}/{len(successful_transcriptions)} enhanced")
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"❌ Aya Expanse enhancement error: {str(e)}")
            return transcriptions
    
    def _parse_aya_response(self, response_text: str, expected_count: int) -> List[str]:
        """Parse Aya Expanse response to extract enhanced transcriptions."""
        try:
            lines = response_text.strip().split('\n')
            enhanced_texts = []
            
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#') and not line.startswith('Enhanced'):
                    # Remove numbering if present
                    if '. ' in line and line.split('. ', 1)[0].isdigit():
                        line = line.split('. ', 1)[1]
                    enhanced_texts.append(line)
            
            # Ensure we have the expected number of results
            while len(enhanced_texts) < expected_count:
                enhanced_texts.append("")  # Add empty strings for missing results
            
            return enhanced_texts[:expected_count]
            
        except Exception as e:
            logger.error(f"❌ Failed to parse Aya Expanse response: {str(e)}")
            return [""] * expected_count


def enhance_transcriptions_with_llm(
    transcriptions: List[Dict[str, Any]],
    output_dir: Path,
    config: Dict[str, Any]
) -> List[Dict[str, Any]]:
    """
    Enhance transcriptions using the configured LLM.
    
    Args:
        transcriptions: List of transcription results from Canary
        output_dir: Directory to save enhanced results
        config: Configuration dictionary
        
    Returns:
        List of enhanced transcription results
    """
    if not transcriptions:
        logger.warning("⚠️  No transcriptions provided for enhancement")
        return []
    
    provider = config.get("llm_provider", "gemini")
    logger.info(f"🤖 Starting LLM enhancement with {provider}")
    
    # Initialize appropriate processor
    if provider == "gemini":
        processor = GeminiPostProcessor(config)
    elif provider == "aya-expanse":
        processor = AyaExpansePostProcessor(config)
    else:
        logger.error(f"❌ Unknown LLM provider: {provider}")
        return transcriptions
    
    try:
        # Enhance transcriptions
        enhanced_results = processor.enhance_transcriptions(transcriptions)
        
        # Save enhanced results
        results_file = output_dir / "llm_enhanced_transcriptions.json"
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(enhanced_results, f, indent=2, ensure_ascii=False)
        
        logger.info(f"💾 Enhanced transcriptions saved to: {results_file}")
        
        return enhanced_results
        
    except Exception as e:
        logger.error(f"❌ LLM enhancement failed: {str(e)}")
        return transcriptions


if __name__ == "__main__":
    # Test LLM availability
    logger.info("🧪 Testing LLM availability...")
    
    if GEMINI_AVAILABLE:
        logger.info("✅ Google Generative AI available")
    else:
        logger.warning("⚠️  Google Generative AI not available")
    
    if LLAMA_CPP_AVAILABLE:
        logger.info("✅ llama-cpp-python available")
    else:
        logger.warning("⚠️  llama-cpp-python not available")
