# AutoSub with LLM Configuration
# Configuration file for the AutoSub with LLM pipeline

# Authentication
huggingface_token: "your_huggingface_token_here"  # Required for pyannote models
gemini_api_key: "your_gemini_api_key_here"  # Required for Gemini LLM (or set GEMINI_API_KEY env var)

# Device and performance settings
device: "cuda"  # Use "cpu" if no GPU is available
autocast_dtype: "bf16"  # Options: "fp32", "bf16", "fp16", or null (to disable autocast)

# NVIDIA Canary Model Settings
canary_batch_size: 4  # Batch size for Canary transcription (adjust based on GPU memory)

# LLM Configuration
llm_provider: "gemini"  # Options: "gemini", "aya-expanse"
llm_batch_size: 8  # Number of transcriptions to process in each LLM batch
llm_prompt_template: |
  You are an expert transcription editor. Your task is to fix and improve the following batch of ASR (Automatic Speech Recognition) transcriptions for accuracy and context.

  Instructions:
  1. Fix obvious ASR errors and misheard words
  2. Improve punctuation and capitalization
  3. Maintain speaker consistency across segments
  4. Ensure contextual coherence between segments
  5. Preserve the original meaning and timing structure
  6. Do not add content that wasn't spoken
  7. Keep the same number of segments as input

  Input transcriptions:
  {transcriptions}

  Please provide the enhanced transcriptions in the same order, one per line:

# Gemini-specific settings
gemini_model: "gemini-1.5-flash"  # Gemini model to use
gemini_rate_limit: 1.0  # Seconds to wait between API calls

# Aya Expanse (local LLM) settings
aya_context_length: 4096  # Context length for Aya Expanse
aya_threads: 4  # Number of CPU threads for Aya Expanse
aya_max_tokens: 1024  # Maximum tokens to generate
aya_temperature: 0.3  # Temperature for text generation (0.0-1.0)

# Denoising configuration (Resemble Enhance settings)
# MAXIMUM QUALITY SETTINGS - Configured for highest possible audio quality
denoising:
  sample_rate: 44100        # Sample rate for denoiser processing (44.1kHz recommended)
  chunk_seconds: 60.0       # Duration of each processing chunk (longer = better quality)
  overlap_seconds: 2.0      # Overlap between chunks (more overlap = smoother transitions)
  denoise_before_enhancement: true  # Whether to denoise before enhancement

  # CFM (Conditional Flow Matching) Settings - MAXIMUM QUALITY
  # These settings match the HuggingFace interface for maximum quality output
  cfm_steps: 128           # CFM function evaluations (64-512, MAXIMUM for best quality)
  cfm_temperature: 0.6     # CFM prior temperature (0.1-1.0, lower = more stable/deterministic)
  solver: "midpoint"              # CFM solver: "euler" (fastest), "midpoint", "rk4" (MAXIMUM quality)

  # Precision settings for maximum quality
  use_fp16: false          # Use FP32/FP64 for maximum precision (slower but highest quality)

# Audio cleaning configuration
cleaning:
  min_duration: 1.0        # Minimum audio duration in seconds (files shorter than this will be excluded)
  bandwidth_threshold: 5000 # Frequency threshold in Hz for noise detection (lower = more strict)

# SRT Generation Settings
srt_max_line_length: 42  # Maximum characters per line in SRT subtitles
srt_max_lines: 2  # Maximum lines per subtitle
include_speaker_labels: true  # Include speaker labels in subtitles
generate_comparison_srt: false  # Generate comparison SRT showing original vs enhanced

# Processing Settings
execution_mode: "step_sequential"  # Process all files through each step before moving to next step
skip_cleaning: true  # Skip the cleaning step (04_cleaned) for AutoSub pipeline
generate_srt: true  # Generate SRT files as final output
keep_intermediates: false  # Keep intermediate processing files for debugging

# File Extensions
video_extensions: ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv', '.webm']
audio_extensions: ['.wav', '.mp3', '.flac', '.m4a', '.ogg']

# Temporary Directory (optional)
# temp_dir: "D:/temp"  # Custom temporary directory location (uncomment to use)

# Model parameters (for compatibility with existing pipeline)
chunk_size: 64
left_context_size: 128
right_context_size: 128
total_batch_duration: 14400  # Maximum audio duration in seconds to process at once

# Output options
print_results: true
save_results: true

# HuggingFace dataset upload configuration (optional)
huggingface:
  dataset_name: "autosub-processed-dataset"  # Name for the dataset
  repo_id: "your_username/your-dataset-name"  # HuggingFace repository ID
  private: false  # Whether the dataset should be private
